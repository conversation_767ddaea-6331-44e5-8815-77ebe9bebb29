import fs from 'fs'
import path from 'path'

const routesDir = './src/routes'
const files = [
  'auth.js',
  'users.js',
  'groups.js', 
  'roles.js',
  'modules.js',
  'permissions.js',
  'access.js'
]

files.forEach(file => {
  const filePath = path.join(routesDir, file)
  const content = fs.readFileSync(filePath, 'utf8')
  
  let esmContent = content
    .replace(/const express = require\('express'\)/g, "import express from 'express'")
    .replace(/const \{ ([^}]+) \} = require\('([^']+)'\)/g, "import { $1 } from '$2.js'")
    .replace(/const ([^=\s]+) = require\('([^']+)'\)/g, "import $1 from '$2.js'")
    .replace(/module\.exports = ([^;]+)/g, "export default $1")
  
  const esmFile = file.replace('.js', '-esm.js')
  const esmPath = path.join(routesDir, esmFile)
  fs.writeFileSync(esmPath, esmContent)
  console.log(`Converted ${file} to ${esmFile}`)
})
