/**
 * Simple API test script to verify the IAM system is working correctly
 * Run with: node test-api.js
 */

import axios from 'axios'

const BASE_URL = 'http://localhost:3000'
let adminToken = ''
let userToken = ''

// Test configuration
const testConfig = {
  adminUser: {
    email: '<EMAIL>',
    password: 'Password123!'
  },
  regularUser: {
    email: '<EMAIL>',
    password: 'Password123!'
  }
}

// Helper function to make API calls
const apiCall = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` })
      },
      ...(data && { data })
    }

    const response = await axios(config)
    return { success: true, data: response.data }
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    }
  }
}

// Test functions
const testHealthCheck = async () => {
  console.log('\n🔍 Testing health check...')
  const result = await apiCall('GET', '/health')
  
  if (result.success) {
    console.log('✅ Health check passed')
    return true
  } else {
    console.log('❌ Health check failed:', result.error)
    return false
  }
}

const testAdminLogin = async () => {
  console.log('\n🔍 Testing admin login...')
  const result = await apiCall('POST', '/api/auth/login', testConfig.adminUser)
  
  if (result.success && result.data.data.token) {
    adminToken = result.data.data.token
    console.log('✅ Admin login successful')
    return true
  } else {
    console.log('❌ Admin login failed:', result.error)
    return false
  }
}

const testUserLogin = async () => {
  console.log('\n🔍 Testing user login...')
  const result = await apiCall('POST', '/api/auth/login', testConfig.regularUser)
  
  if (result.success && result.data.data.token) {
    userToken = result.data.data.token
    console.log('✅ User login successful')
    return true
  } else {
    console.log('❌ User login failed:', result.error)
    return false
  }
}

const testAdminPermissions = async () => {
  console.log('\n🔍 Testing admin permissions...')
  const result = await apiCall('GET', '/api/me/permissions', null, adminToken)
  
  if (result.success && result.data.data.totalPermissions > 0) {
    console.log(`✅ Admin has ${result.data.data.totalPermissions} permissions`)
    return true
  } else {
    console.log('❌ Admin permissions test failed:', result.error)
    return false
  }
}

const testUserPermissions = async () => {
  console.log('\n🔍 Testing user permissions...')
  const result = await apiCall('GET', '/api/me/permissions', null, userToken)
  
  if (result.success) {
    console.log(`✅ User has ${result.data.data.totalPermissions} permissions`)
    return true
  } else {
    console.log('❌ User permissions test failed:', result.error)
    return false
  }
}

const testPermissionSimulation = async () => {
  console.log('\n🔍 Testing permission simulation...')
  
  // Test admin can create users
  const adminCreateTest = await apiCall('POST', '/api/simulate-action', {
    module: 'Users',
    action: 'create'
  }, adminToken)
  
  if (!adminCreateTest.success || !adminCreateTest.data.data.simulation.hasPermission) {
    console.log('❌ Admin should be able to create users')
    return false
  }
  
  // Test user cannot create users
  const userCreateTest = await apiCall('POST', '/api/simulate-action', {
    module: 'Users',
    action: 'create'
  }, userToken)
  
  if (!userCreateTest.success || userCreateTest.data.data.simulation.hasPermission) {
    console.log('❌ Regular user should not be able to create users')
    return false
  }
  
  // Test user can read users
  const userReadTest = await apiCall('POST', '/api/simulate-action', {
    module: 'Users',
    action: 'read'
  }, userToken)
  
  if (!userReadTest.success || !userReadTest.data.data.simulation.hasPermission) {
    console.log('❌ Regular user should be able to read users')
    return false
  }
  
  console.log('✅ Permission simulation tests passed')
  return true
}

const testUnauthorizedAccess = async () => {
  console.log('\n🔍 Testing unauthorized access...')
  
  // Test accessing protected route without token
  const noTokenTest = await apiCall('GET', '/api/users')
  
  if (noTokenTest.success || noTokenTest.status !== 401) {
    console.log('❌ Should require authentication')
    return false
  }
  
  console.log('✅ Unauthorized access properly blocked')
  return true
}

const testModuleOperations = async () => {
  console.log('\n🔍 Testing module operations...')
  
  // Test getting modules as admin
  const modulesTest = await apiCall('GET', '/api/modules', null, adminToken)
  
  if (!modulesTest.success) {
    console.log('❌ Admin should be able to get modules:', modulesTest.error)
    return false
  }
  
  console.log(`✅ Found ${modulesTest.data.data.modules.length} modules`)
  return true
}

// Main test runner
const runTests = async () => {
  console.log('🚀 Starting IAM System API Tests')
  console.log('=====================================')
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Admin Login', fn: testAdminLogin },
    { name: 'User Login', fn: testUserLogin },
    { name: 'Admin Permissions', fn: testAdminPermissions },
    { name: 'User Permissions', fn: testUserPermissions },
    { name: 'Permission Simulation', fn: testPermissionSimulation },
    { name: 'Unauthorized Access', fn: testUnauthorizedAccess },
    { name: 'Module Operations', fn: testModuleOperations }
  ]
  
  let passed = 0
  let failed = 0
  
  for (const test of tests) {
    try {
      const result = await test.fn()
      if (result) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message)
      failed++
    }
  }
  
  console.log('\n=====================================')
  console.log('🏁 Test Results:')
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`📊 Total: ${passed + failed}`)
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! IAM system is working correctly.')
    process.exit(0)
  } else {
    console.log('\n⚠️  Some tests failed. Please check the system configuration.')
    process.exit(1)
  }
}

// Run tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error)
  process.exit(1)
})
