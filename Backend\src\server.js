require('dotenv').config()
const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const morgan = require('morgan')
const { initializeDatabase } = require('./config/database')

// Import routes
const authRoutes = require('./routes/auth')
const userRoutes = require('./routes/users')
const groupRoutes = require('./routes/groups')
const roleRoutes = require('./routes/roles')
const moduleRoutes = require('./routes/modules')
const permissionRoutes = require('./routes/permissions')
const accessRoutes = require('./routes/access')

const app = express()
const PORT = process.env.PORT || 3000

// Middleware
app.use(helmet())
app.use(
  cors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
    credentials: true
  })
)
app.use(morgan('combined'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'IAM System API is running',
    timestamp: new Date().toISOString()
  })
})

// Routes
app.use('/api/auth', authRoutes)
app.use('/api/users', userRoutes)
app.use('/api/groups', groupRoutes)
app.use('/api/roles', roleRoutes)
app.use('/api/modules', moduleRoutes)
app.use('/api/permissions', permissionRoutes)
app.use('/api', accessRoutes)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  })
})

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error)

  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.stack : undefined
  })
})

// Start server with database initialization
const startServer = async () => {
  try {
    // Initialize SQLite database
    await initializeDatabase()

    // Start the server
    app.listen(PORT, () => {
      console.log(`🚀 IAM System API server running on port ${PORT}`)
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`)
      console.log(`🔗 Health check: http://localhost:${PORT}/health`)
      console.log(`💾 Database: SQLite`)
    })
  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

startServer()

module.exports = app
