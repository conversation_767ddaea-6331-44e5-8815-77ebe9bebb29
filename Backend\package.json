{"name": "iam-backend", "version": "1.0.0", "description": "Identity and Access Management System Backend", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node src/utils/seedData.js", "test": "node test-api.js"}, "keywords": ["iam", "authentication", "authorization", "express", "prisma"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.13.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1"}, "devDependencies": {"nodemon": "^3.1.10", "prisma": "^6.13.0"}}