import fs from 'fs'
import path from 'path'

const controllersDir = './src/controllers'
const files = [
  'userController.js',
  'groupController.js',
  'roleController.js',
  'moduleController.js',
  'permissionController.js',
  'accessController.js'
]

files.forEach(file => {
  const filePath = path.join(controllersDir, file)
  const content = fs.readFileSync(filePath, 'utf8')

  let esmContent = content
    .replace(/const \{ ([^}]+) \} = require\('([^']+)'\)/g, "import { $1 } from '$2.js'")
    .replace(/const ([^=\s]+) = require\('([^']+)'\)/g, "import $1 from '$2.js'")
    .replace(/module\.exports = \{([^}]+)\}/g, 'export { $1 }')

  const esmFile = file.replace('.js', '-esm.js')
  const esmPath = path.join(controllersDir, esmFile)
  fs.writeFileSync(esmPath, esmContent)
  console.log(`Converted ${file} to ${esmFile}`)
})
