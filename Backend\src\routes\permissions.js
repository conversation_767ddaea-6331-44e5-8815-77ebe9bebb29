import express from 'express';
const {
  getAllPermissions,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission
} = require('../controllers/permissionController');
const {
  validateCreatePermission,
  validateUpdatePermission,
  validateId,
  validatePagination
} = require('../middleware/validation');
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// All permission routes require authentication
router.use(authenticateToken);

// GET /api/permissions - Get all permissions with pagination
router.get('/', validatePagination, getAllPermissions);

// GET /api/permissions/:id - Get permission by ID
router.get('/:id', validateId, getPermissionById);

// POST /api/permissions - Create new permission
router.post('/', validateCreatePermission, createPermission);

// PUT /api/permissions/:id - Update permission
router.put('/:id', validateId, validateUpdatePermission, updatePermission);

// DELETE /api/permissions/:id - Delete permission (soft delete)
router.delete('/:id', validateId, deletePermission);

export default router;
