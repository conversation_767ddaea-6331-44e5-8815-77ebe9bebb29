import express from 'express';
const {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
} = require('../controllers/userController');
const {
  validateCreateUser,
  validateUpdateUser,
  validateId,
  validatePagination
} = require('../middleware/validation');
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// All user routes require authentication
router.use(authenticateToken);

// GET /api/users - Get all users with pagination
router.get('/', validatePagination, getAllUsers);

// GET /api/users/:id - Get user by ID
router.get('/:id', validateId, getUserById);

// POST /api/users - Create new user
router.post('/', validateCreateUser, createUser);

// PUT /api/users/:id - Update user
router.put('/:id', validateId, validateUpdateUser, updateUser);

// DELETE /api/users/:id - Delete user (soft delete)
router.delete('/:id', validateId, deleteUser);

export default router;
