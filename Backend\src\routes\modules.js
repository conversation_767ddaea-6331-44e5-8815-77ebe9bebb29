import express from 'express';
const {
  getAllModules,
  getModuleById,
  createModule,
  updateModule,
  deleteModule
} = require('../controllers/moduleController');
const {
  validateCreateModule,
  validateUpdateModule,
  validateId,
  validatePagination
} = require('../middleware/validation');
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// All module routes require authentication
router.use(authenticateToken);

// GET /api/modules - Get all modules with pagination
router.get('/', validatePagination, getAllModules);

// GET /api/modules/:id - Get module by ID
router.get('/:id', validateId, getModuleById);

// POST /api/modules - Create new module
router.post('/', validateCreateModule, createModule);

// PUT /api/modules/:id - Update module
router.put('/:id', validateId, validateUpdateModule, updateModule);

// DELETE /api/modules/:id - Delete module (soft delete)
router.delete('/:id', validateId, deleteModule);

export default router;
