import express from 'express'
const {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  assignRolesToGroup
} = require('../controllers/roleController')
import { assignPermissionsToRole } from '../controllers/permissionController.js'
const {
  validateCreateRole,
  validateUpdateRole,
  validateAssignRoleToGroup,
  validateAssignPermissionToRole,
  validateId,
  validatePagination
} = require('../middleware/validation')
import { authenticateToken } from '../middleware/auth.js'

const router = express.Router()

// All role routes require authentication
router.use(authenticateToken)

// GET /api/roles - Get all roles with pagination
router.get('/', validatePagination, getAllRoles)

// GET /api/roles/:id - Get role by ID
router.get('/:id', validateId, getRoleById)

// POST /api/roles - Create new role
router.post('/', validateCreateRole, createRole)

// PUT /api/roles/:id - Update role
router.put('/:id', validateId, validateUpdateRole, updateRole)

// DELETE /api/roles/:id - Delete role (soft delete)
router.delete('/:id', validateId, deleteRole)

// POST /api/roles/:id/permissions - Assign permissions to role
router.post('/:id/permissions', validateId, validateAssignPermissionToRole, assignPermissionsToRole)

export default router
