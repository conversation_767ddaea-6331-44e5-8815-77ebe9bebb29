const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error']
})

// Initialize SQLite database
const initializeDatabase = async () => {
  try {
    console.log('🗄️  Initializing SQLite database...')

    // Test connection and ensure database is ready
    await prisma.$connect()
    console.log('✅ SQLite database connected')

    // Auto-seed the database with sample data
    console.log('🌱 Seeding database with sample data...')
    const { seedDatabase } = require('../utils/seedData')
    await seedDatabase()
    console.log('✅ Database seeded successfully')
  } catch (error) {
    console.error('❌ Failed to initialize database:', error)
    throw error
  }
}

// Handle graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

process.on('SIGINT', async () => {
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  await prisma.$disconnect()
  process.exit(0)
})

module.exports = { prisma, initializeDatabase }
